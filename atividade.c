#include <stdio.h>

void printTabuleiro(char (*p)[4])
{
    for (int i = 0; i < 4; i++)
    {
        for (int j = 0; j < 4; j++)
        {
            printf("%c", *(*(p + i) + j));
            printf(" ");
        }
        printf("\n");
    }
}

void jogadaJogador(char (*p)[4], int *linha, int *coluna, int *jogadas)
{
    if (*(*(p + *linha) + *coluna) == '_')
    {
        *(*(p + *linha) + *coluna) = *jogadas % 2 == 0 ? 'X' : 'O';
        *jogadas -= 1;
    }
    else
    {
        printf("Casa ja preenchida, tente novamente.\n");
    }
}

void jogar()
{
    int jogadas = 16;
    int *jogadasPointer = &jogadas;
    char tabuleiro[4][4] = {{'_', '_', '_', '_'},
                           {'_', '_', '_', '_'},
                           {'_', '_', '_', '_'},
                           {'_', '_', '_', '_'}};
    printTabuleiro(tabuleiro);

    do
    {
        int linha, coluna;
        int *linhaPointer = &linha;
        int *colunaPointer = &coluna;
        printf("Digite linha e coluna (0-3):\n");
        scanf("%d %d", linhaPointer, colunaPointer);
        jogadaJogador(tabuleiro, linhaPointer, colunaPointer, jogadasPointer);
        printTabuleiro(tabuleiro);

    } while (*jogadasPointer != 0);

    return;
}

int main()
{
    int opcao = 0;
    int *opcaoPointer = &opcao;

    do
    {
        printf("1) Jogar\n");
        printf("2) Sair\n");
        scanf("%d", opcaoPointer);

        switch (*opcaoPointer)
        {
        case 1:
            jogar();
            *opcaoPointer = 0;
            break;
        case 2:
            printf("Saindo...\n");
            break;
        default:
            break;
        }
    } while (*opcaoPointer != 2);

    return 0;
}
