#include <stdio.h>
#include <stdlib.h>

int main() {
    int *opcao = (int*)malloc(sizeof(int));
    char tabuleiro[4][4];
    char *jogador = (char*)malloc(sizeof(char));
    int *linha = (int*)malloc(sizeof(int));
    int *coluna = (int*)malloc(sizeof(int));
    int *casasPreenchidas = (int*)malloc(sizeof(int));

    do {
        printf("1) Jogar\n");
        printf("2) Sair\n");
        scanf("%d", opcao);

        if (*opcao == 1) {
            // Criar tabuleiro 4x4
            int i, j;
            for (i = 0; i < 4; i++) {
                for (j = 0; j < 4; j++) {
                    *(*(tabuleiro + i) + j) = ' ';
                }
            }

            *jogador = 'X';
            *casasPreenchidas = 0;

            // <PERSON><PERSON> ate todas as casas estarem preenchidas
            while (*casasPreenchidas < 16) {
                // Mostrar tabuleiro
                printf("\nTabuleiro:\n");
                for (i = 0; i < 4; i++) {
                    for (j = 0; j < 4; j++) {
                        printf(" %c ", *(*(tabuleiro + i) + j));
                        if (j < 3) printf("|");
                    }
                    printf("\n");
                    if (i < 3) printf("-----------\n");
                }

                // Jogador escolhe posicao
                printf("Jogador %c, digite linha (0-3): ", *jogador);
                scanf("%d", linha);
                printf("Digite coluna (0-3): ", *jogador);
                scanf("%d", coluna);

                // Verificar se casa ja esta preenchida
                if (*(*(tabuleiro + *linha) + *coluna) != ' ') {
                    printf("Casa ja preenchida! Tente novamente.\n");
                } else {
                    // Colocar peca
                    *(*(tabuleiro + *linha) + *coluna) = *jogador;
                    (*casasPreenchidas)++;

                    // Intercalar jogador
                    if (*jogador == 'X') {
                        *jogador = 'O';
                    } else {
                        *jogador = 'X';
                    }
                }
            }

            // Mostrar tabuleiro final
            printf("\nTabuleiro final:\n");
            for (i = 0; i < 4; i++) {
                for (j = 0; j < 4; j++) {
                    printf(" %c ", *(*(tabuleiro + i) + j));
                    if (j < 3) printf("|");
                }
                printf("\n");
                if (i < 3) printf("-----------\n");
            }
            printf("Jogo finalizado!\n");
        }

    } while (*opcao != 2);

    free(opcao);
    free(jogador);
    free(linha);
    free(coluna);
    free(casasPreenchidas);

    return 0;
}
