#include <stdio.h>
#include <stdlib.h>

void inicializar<PERSON><PERSON><PERSON><PERSON>(char (*tabuleiro)[4]) {
    int i, j;
    for (i = 0; i < 4; i++) {
        for (j = 0; j < 4; j++) {
            *(*(tabuleiro + i) + j) = ' ';
        }
    }
}

void exibirTabuleiro(char (*tabuleiro)[4]) {
    int i, j;
    printf("\n   0   1   2   3\n");
    for (i = 0; i < 4; i++) {
        printf("%d ", i);
        for (j = 0; j < 4; j++) {
            printf(" %c ", *(*(tabuleiro + i) + j));
            if (j < 3) printf("|");
        }
        printf("\n");
        if (i < 3) {
            printf("  -----------\n");
        }
    }
    printf("\n");
}

int verificarPosicaoValida(char (*tabuleiro)[4], int *linha, int *coluna) {
    if (*linha < 0 || *linha > 3 || *coluna < 0 || *coluna > 3) {
        return 0; // Posição inválida
    }
    if (*(*(tabuleiro + *linha) + *coluna) != ' ') {
        return 0; // Posição já ocupada
    }
    return 1; // Posição válida
}

int tabuleiroCompleto(char (*tabuleiro)[4]) {
    int i, j;
    for (i = 0; i < 4; i++) {
        for (j = 0; j < 4; j++) {
            if (*(*(tabuleiro + i) + j) == ' ') {
                return 0; // Ainda há espaços vazios
            }
        }
    }
    return 1; // Tabuleiro completo
}

void jogar() {
    char tabuleiro[4][4];
    char *jogadorAtual = (char*)malloc(sizeof(char));
    int *linha = (int*)malloc(sizeof(int));
    int *coluna = (int*)malloc(sizeof(int));
    int *numeroJogador = (int*)malloc(sizeof(int));

    // Inicializar variáveis usando ponteiros
    *jogadorAtual = 'X';
    *numeroJogador = 1;

    inicializarTabuleiro(tabuleiro);

    printf("\n=== JOGO DA VELHA 4x4 ===\n");
    printf("Jogador 1: X\n");
    printf("Jogador 2: O\n");

    while (!tabuleiroCompleto(tabuleiro)) {
        exibirTabuleiro(tabuleiro);

        printf("Jogador %d (%c), escolha sua jogada:\n", *numeroJogador, *jogadorAtual);

        do {
            printf("Digite a linha (0-3): ");
            scanf("%d", linha);
            printf("Digite a coluna (0-3): ");
            scanf("%d", coluna);

            if (!verificarPosicaoValida(tabuleiro, linha, coluna)) {
                printf("Posição inválida ou já ocupada! Tente novamente.\n");
            }
        } while (!verificarPosicaoValida(tabuleiro, linha, coluna));

        // Colocar a peça no tabuleiro
        *(*(tabuleiro + *linha) + *coluna) = *jogadorAtual;

        // Alternar jogador
        if (*jogadorAtual == 'X') {
            *jogadorAtual = 'O';
            *numeroJogador = 2;
        } else {
            *jogadorAtual = 'X';
            *numeroJogador = 1;
        }
    }

    exibirTabuleiro(tabuleiro);
    printf("Jogo finalizado! Todas as casas foram preenchidas.\n");

    // Liberar memória
    free(jogadorAtual);
    free(linha);
    free(coluna);
    free(numeroJogador);
}

int main() {
    int *opcao = (int*)malloc(sizeof(int));

    do {
        printf("\n=== MENU ===\n");
        printf("1) Jogar\n");
        printf("2) Sair\n");
        printf("Escolha uma opção: ");
        scanf("%d", opcao);

        switch (*opcao) {
            case 1:
                jogar();
                break;
            case 2:
                printf("Saindo do jogo...\n");
                break;
            default:
                printf("Opção inválida! Tente novamente.\n");
                break;
        }
    } while (*opcao != 2);

    free(opcao);
    return 0;
}
